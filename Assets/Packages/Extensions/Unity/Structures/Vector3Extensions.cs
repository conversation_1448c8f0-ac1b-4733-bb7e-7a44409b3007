using System.Runtime.CompilerServices;

using UnityEngine;

namespace Utils.Extensions
{
    public static class Vector3Extensions
    {
        private static Collider2D[] _colliders = new Collider2D[1];

        public static Quaternion ToRotation2D(this Vector3 direction)
        {
            return Quaternion.AngleAxis(Mathf.Atan2(-direction.x, direction.y) * Mathf.Rad2Deg, Vector3.forward);
        }

        public static Quaternion ToRotation3D(this Vector3 direction, Vector3 up)
        {
            return Quaternion.LookRotation(direction, up);
        }

        public static Quaternion ToLocalRotation(this Vector3 worldDirection, Quaternion worldZenithRotation, float clampArk)
        {
            var worldZenithAngle = worldZenithRotation.eulerAngles.z;
            var worldDirectionAngle = Mathf.Atan2(-worldDirection.x, worldDirection.y) * Mathf.Rad2Deg;

            var localDirectionAngle = worldDirectionAngle - worldZenithAngle;
            localDirectionAngle += localDirectionAngle switch
            {
                < -180 => +360,
                > +180 => -360,
                _ => 0
            };

            var halfArk = clampArk / 2;
            localDirectionAngle = Mathf.Clamp(localDirectionAngle, -halfArk, halfArk);
            return Quaternion.AngleAxis(localDirectionAngle, Vector3.forward);
        }

        public static Vector3 Around(this Vector3 source, float magnitude)
        {
            return source + magnitude * (Quaternion.Euler(0, 0, Random.Range(0, 359)) * Vector3.up);
        }

        public static Vector3 RandomAround(this Vector3 source, float offset)
        {
            return new(source.x + Random.Range(-offset, offset), source.y + Random.Range(-offset, offset), source.z);
        }

        public static Vector3 RandomAroundNoCollision(this Vector3 source, float offset, LayerMask layerMask, float angleStep)
        {
            var offsetVector = (Vector3)Random.insideUnitCircle * offset;

            if (layerMask == 0)
                return source + offsetVector;

            for (var angle = 0f; angle < 360f; angle += angleStep)
            {
                if (Physics2D.OverlapPoint(source + offsetVector, layerMask) == null)
                    break;

                offsetVector = Quaternion.Euler(0f, 0f, angleStep) * offsetVector;
            }

            return source + offsetVector;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3 Inverse(this Vector3 source, float multiplier = 1f)
        {
            return new(multiplier / source.x, multiplier / source.y, multiplier / source.z);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3 WithX(this Vector3 source, float xValue)
        {
            return new(xValue, source.y, source.z);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3 WithY(this Vector3 source, float yValue)
        {
            return new(source.x, yValue, source.z);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector3 WithZ(this Vector3 source, float zValue)
        {
            return new(source.x, source.y, zValue);
        }

        public static Vector3 RandomDirection(System.Random random)
        {
            return random == null ? Random.insideUnitSphere : new Vector3((float)random.NextDouble() - 0.5f,
                                                                          (float)random.NextDouble() - 0.5f,
                                                                          (float)random.NextDouble() - 0.5f).normalized;
        }

        public static Vector3 RandomPositionInBox(Vector3 size, System.Random random)
        {
            var halfSize = size / 2f;

            return random == null ?
                    new Vector3(Random.Range(-halfSize.x, halfSize.x),
                                Random.Range(-halfSize.y, halfSize.y),
                                Random.Range(-halfSize.z, halfSize.z))
                  : new Vector3(((float)random.NextDouble() - 0.5f) * 2 * halfSize.x,
                                ((float)random.NextDouble() - 0.5f) * 2 * halfSize.y,
                                ((float)random.NextDouble() - 0.5f) * 2) * halfSize.z;
        }

        public static Vector3 RandomPositionInBox(float size, System.Random random)
        {
            return RandomPositionInBox(new Vector3(size, size, size), random);
        }

        public static void Deconstruct(this Vector3 vector3, out float x, out float y, out float z)
        {
            x = vector3.x;
            y = vector3.y;
            z = vector3.z;
        }

        public static Vector3 MultiplyByComponent(this Vector3 source, Vector3 multiplier)
        {
            return new(source.x * multiplier.x, source.y * multiplier.y, source.z * multiplier.z);
        }

        public static Vector3 DivideByComponent(this Vector3 source, Vector3 divider)
        {
            return new(source.x / divider.x, source.y / divider.y, source.z / divider.z);
        }

        public static Vector3 LerpByComponent(Vector3 a, Vector3 b, Vector3 t)
        {
            return new(Mathf.Lerp(a.x, b.x, t.x), Mathf.Lerp(a.y, b.y, t.y), Mathf.Lerp(a.z, b.z, t.z));
        }
    }
}