using System;
using System.Collections;
using System.Collections.Generic;
using Consts;
using Drawing;
using FMOD.Studio;
using FMODUnity;
using Rewired;
using Sirenix.OdinInspector;
using Unity.Netcode;
using UnityEngine;
using Utils.Debug;
using Utils.Extensions;

[HelpURL(StringConsts.DocumentationLink + "NT-A-73")]
public class VacuumCleaner : GenericTool
{
    #region Debug

    private static DebugLevel DebugLevel => DebugMessengerProvider.GetLevel(type, DebugLevel.Warning);
    private static readonly System.Type type = typeof(VacuumCleaner);

    #endregion

    [SerializeField] private NetworkObject enginePrefab;
    [SerializeField] private SpringJoint springJoint;
    [SerializeField] private Transform hosePoint;

    [SerializeField] private float suctionForce = 10f;
    [SerializeField] private Transform vacuumingPoint;

    [SerializeField] private float maxAffectedMass;

    [SerializeField] private Transform handleEndPart;

    [SerializeField] private SphereCollider vacuumCast;
    [SerializeField] private LayerMask layersToVacuum;

    [SerializeField] private Collider swallowingCollider;
    [SerializeField] private EventReference vacuumingLoop;
    [SerializeField] private int startStopSectionDurationMS;
    [SerializeField] private int totalEventDurationMS;

    [SerializeField] private EventReference garbageCollectedSoundFX;
    [SerializeField] private float swallowDuration = 0.2f;
    [SerializeField] private AnimationCurve swallowScalePunch;
    private int _lastScaleChangeFrame;

    [SerializeField, FoldoutGroup(StringConsts.DebugFoldoutGroup)]
    private bool debugGizmos = true;

    [SerializeField] private float weightThreshold = 10f;
    private float totalGarbageMass = 0f;
    private GarbageBagEjector bagSpawner;

    [SerializeField] private ParticleSystem suctionVFX;
    private RaycastHit[] _raycastHits = new RaycastHit[4];
    private Vector3 _defaultVacuumPointLocalOffset;
    private float _vacuumColliderRadius = 0f;
    private float _constantVacuumPointOffsetInTool = 0f;
    private float _defaultVacuumPointDistance = 5f;

    public override int IdleAnimationTrigger => AnimatorConsts.VacuumCleaner;

    private Collider[] _overlapResults = new Collider[128];

    private HashSet<Rigidbody> _affectedRigidbodiesThisFrame = new();

    private NetworkVariable<bool> _networkIsVacuuming = new(false);
    private Transform _engineBodyTransform;
    private EventInstance _vacuumingLoopInstance = default;

    private NetworkObject _engine;

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();
        swallowingCollider.enabled = false;

        if (IsServer)
            SpawnEngine();
    }

    private void SpawnEngine()
    {
        _engine = Instantiate(enginePrefab, transform.position + new Vector3(0, 0f, -0.4f), Quaternion.Euler(0, 180, 0));
        _engine.Spawn();
        _engine.gameObject.MoveToWorld();
        SetupEngineClientRPC(_engine);
    }

    private void OnEnable()
    {
        if (_engine != null)
            _engine.gameObject.SetActive(true);
    }

    private void OnDisable()
    {
        if (_engine != null)
            _engine.gameObject.SetActive(false);
    }

    public override void OnNetworkDespawn()
    {
        base.OnNetworkDespawn();

        if (IsServer)
            DespawnEngine();
    }

    private void DespawnEngine()
    {
        if (_engine.IsSpawned)
            _engine.Despawn();
        _engine = null;
    }

    private void Start()
    {
        if (swallowingCollider is CapsuleCollider capsule)
            _vacuumColliderRadius = capsule.radius;

        _defaultVacuumPointLocalOffset = vacuumingPoint.localPosition;
        _defaultVacuumPointDistance = Vector3.Distance(transform.position, vacuumingPoint.position);
        _constantVacuumPointOffsetInTool = _defaultVacuumPointDistance - _defaultVacuumPointLocalOffset.z;

        _networkIsVacuuming.OnValueChanged += (bool oldValue, bool newValue) => OnVacuumingValueChanged(oldValue, newValue);
    }

    [ClientRpc]
    private void SetupEngineClientRPC(NetworkObjectReference engineNetworkObjectReference)
    {
        if (engineNetworkObjectReference.TryGet(out var engineNetworkObject))
        {
            _engineBodyTransform = engineNetworkObject.transform;

            var engineRigidBody = engineNetworkObject.GetComponent<Rigidbody>();
            springJoint.connectedBody = engineRigidBody;

            // not good
            _engine.GetComponentInChildren<CableComponent>().ManualSetup(hosePoint);

            bagSpawner = engineNetworkObject.GetComponent<GarbageBagEjector>();
        }
    }

    /// <summary>
    /// Executes every frame a button is pressed, following the <see cref="InputActionEventType.ButtonPressed"/> subscription
    /// </summary>
    [ServerRpc(RequireOwnership = false)]
    private void ApplySuctionServerRPC(Vector3 castPosition, Vector3 suctionPointPosition)
    {
        var deltaTime = Time.deltaTime;

        if (debugGizmos)
            Draw.ingame.WireSphere(suctionPointPosition, 0.1f, Color.green);

        _overlapResults.ClearToDefault();
        Physics.OverlapSphereNonAlloc(castPosition, vacuumCast.radius, _overlapResults, layersToVacuum);

        if (debugGizmos)
            Draw.ingame.WireSphere(castPosition, vacuumCast.radius, Color.yellow);

        _affectedRigidbodiesThisFrame.Clear();

        for (int i = 0; i < _overlapResults.Length; i++)
        {
            var overlapResult = _overlapResults[i];
            if (overlapResult != null)
            {
                ComponentsCache.GetRigidbody(overlapResult, out var nullableRigidBody);
                if (nullableRigidBody == null)
                    continue;

                if (!nullableRigidBody.isKinematic &&
                    nullableRigidBody.mass <= maxAffectedMass &&
                    !_affectedRigidbodiesThisFrame.Contains(nullableRigidBody))
                {
                    var direction = suctionPointPosition - overlapResult.transform.position;
                    var forceVector = deltaTime * Mathf.Clamp01(direction.magnitude) * nullableRigidBody.mass * suctionForce * direction.normalized;

                    if (debugGizmos)
                    {
                        var suckablePosition = nullableRigidBody.transform.position;
                        Draw.ingame.Line(suckablePosition, suckablePosition + forceVector, Color.red);
                    }

                    nullableRigidBody.AddForce(forceVector, ForceMode.Force);
                    _affectedRigidbodiesThisFrame.Add(nullableRigidBody);
                }
                else
                {
                    if (debugGizmos)
                    {
                        var direction = suctionPointPosition - overlapResult.transform.position;
                        var suckablePosition = nullableRigidBody.transform.position;
                        Draw.ingame.Line(suckablePosition, suckablePosition + direction, Color.blue);
                    }
                }
            }
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        if (GarbageDestructionHandler.HandleCollision(other, (garbage) =>
        {
            if (!garbage.IsSpawned)
            {
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Error))
                    DebugMessenger.Message($"{garbage} is not spawned on network and can not be synced");

                return;
            }
            SuckGarbageInServerRPC(garbage.NetworkObject);
        }))
        {
            SoundFXManager.Instance.PlayOneShot(MultiplayerContext.OriginalClient, garbageCollectedSoundFX.Guid, transform.position, SoundFXTarget.Local);
        }
    }

    [ServerRpc(RequireOwnership = false)]
    private void SuckGarbageInServerRPC(NetworkObjectReference networkObjectReference)
    {
        SuckGarbageInClientRPC(networkObjectReference);
    }

    [ClientRpc]
    private void SuckGarbageInClientRPC(NetworkObjectReference networkObjectReference)
    {
        if (networkObjectReference.TryGet(out var networkObject))
            StartCoroutine(RecycleCoroutine(networkObject, vacuumingPoint.position, swallowDuration));
    }

    // is played on all clients, but only server despawns the object
    private IEnumerator RecycleCoroutine(NetworkObject recycledNetworkObject, Vector3 contactPoint, float timeUntilRecycle)
    {
        var initialPosition = recycledNetworkObject.transform.position;
        var initialScale = recycledNetworkObject.transform.localScale;

        while (timeUntilRecycle > 0 && recycledNetworkObject != null)
        {
            var scaleRatio = timeUntilRecycle / swallowDuration;

            recycledNetworkObject.transform.position = Vector3.Lerp(contactPoint, initialPosition, scaleRatio);
            recycledNetworkObject.transform.localScale = Vector3.Lerp(Vector3.zero, initialScale, scaleRatio);

            if (Time.frameCount != _lastScaleChangeFrame)
            {
                transform.localScale = transform.localScale.WithX(swallowScalePunch.Evaluate(1 - scaleRatio));
                _lastScaleChangeFrame = Time.frameCount;
            }

            timeUntilRecycle -= Time.deltaTime;
            yield return null;
        }

        //transform.localScale = Vector3.one;
        if (IsServer)
        {
            if (!recycledNetworkObject.IsSpawned)
            {
                // can coroutine be started twice on the same object?
                // must have already been despawned
                if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Warning))
                    DebugMessenger.Message($"{recycledNetworkObject} is not spawned on network and can not be despawned");

                yield break;
            }

            var garbageMass = recycledNetworkObject.GetComponent<Rigidbody>().mass;

            SessionStatsManager.Instance.IncrementJobValue(LastHoldingPlayerID, JobType.Garbage);
            recycledNetworkObject.Despawn();

            totalGarbageMass += garbageMass;
            if (totalGarbageMass >= weightThreshold && bagSpawner != null)
            {
                bagSpawner.SpawnBagServerSide(totalGarbageMass, weightThreshold);
                totalGarbageMass = 0f;
            }
        }
    }

    public override void OnUseActive()
    {
        if (!useStarted)
            return;

        // client-authoritative positions
        ApplySuctionServerRPC(vacuumCast.transform.position, vacuumingPoint.position);
    }

    private void OnVacuumingValueChanged(bool oldValue, bool newValue)
    {
        if (DebugMessenger.LevelCondition(DebugLevel, DebugLevel.Message))
            DebugMessenger.Message($"Old value: {oldValue}, New value: {newValue}");

        if (newValue == oldValue)
            return;

        // TODO needs a better fmod event with custom parameters
        if (newValue)
        {
            if (!_vacuumingLoopInstance.isValid())
                _vacuumingLoopInstance = RuntimeManager.CreateInstance(vacuumingLoop.Guid);

            // instance is detached by FMODUnity.RuntimeManager:462 if it stops
            RuntimeManager.AttachInstanceToGameObject(_vacuumingLoopInstance, _engineBodyTransform);

            _vacuumingLoopInstance.getTimelinePosition(out var currentPosition);
            var newPosition = totalEventDurationMS - currentPosition;

            _vacuumingLoopInstance.start();

            if (currentPosition != 0)
                _vacuumingLoopInstance.setTimelinePosition(newPosition);

            suctionVFX.Play();
        }
        else
        {
            _vacuumingLoopInstance.getTimelinePosition(out var currentPosition);
            var newPosition = totalEventDurationMS - (currentPosition < startStopSectionDurationMS ? currentPosition : startStopSectionDurationMS);
            _vacuumingLoopInstance.setTimelinePosition(newPosition);
            _vacuumingLoopInstance.stop(FMOD.Studio.STOP_MODE.ALLOWFADEOUT);
            suctionVFX.Stop();
        }
    }

    public override void OnUseStarted()
    {
        base.OnUseStarted();
        OnUseActive(true);
    }

    public override void OnUseEnded()
    {
        base.OnUseEnded();
        OnUseActive(false);
    }

    private void OnUseActive(bool isActive)
    {
        swallowingCollider.enabled = isActive;

        // fake network bool change locally to get faster response on self
        OnVacuumingValueChanged(_networkIsVacuuming.Value, isActive);
        // actually change network bool to sync the state to other clients
        SetIsVacuumingValueServerRPC(isActive);
    }

    [ServerRpc(RequireOwnership = false)]
    private void SetIsVacuumingValueServerRPC(bool newValue)
    {
        _networkIsVacuuming.Value = newValue;
    }

    private void Update()
    {
        if (!swallowingCollider.enabled)
            return;

        var selfPosition = transform.position;
        var castDistance = _defaultVacuumPointDistance + _vacuumColliderRadius;

        _raycastHits.ClearToDefault();
        if (Physics.RaycastNonAlloc(selfPosition,
                                    handleEndPart.position - selfPosition,
                                    _raycastHits,
                                    castDistance,
                                    MaskConsts.DefaultMask) > 0)
        {
            var minDistance = castDistance;
            for (var i = 0; i < _raycastHits.Length; i++)
            {
                var hitCollider = _raycastHits[i].collider;
                if (hitCollider == null || hitCollider == swallowingCollider)
                    continue;

                if (_raycastHits[i].distance < minDistance)
                    minDistance = _raycastHits[i].distance;
            }

            var newLocalZOffset = minDistance - _constantVacuumPointOffsetInTool - _vacuumColliderRadius;
            var newLocalPosition = _defaultVacuumPointLocalOffset.WithZ(newLocalZOffset);
            vacuumingPoint.localPosition = newLocalPosition;
        }
        else
            vacuumingPoint.localPosition = _defaultVacuumPointLocalOffset;
    }

    protected override void SetCollidersLayer(int layer)
    {
        foreach (var collider in Colliders)
        {
            if (collider != swallowingCollider)
                collider.gameObject.layer = layer;
        }
    }
}
