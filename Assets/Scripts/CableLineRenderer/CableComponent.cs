using UnityEngine;

public class CableComponent : MonoBehaviour
{
    #region Class members

    [SerializeField] private bool manualStart;

    [SerializeField] private Transform endPoint;
    [SerializeField] private Material cableMaterial;

    // Cable config
    [SerializeField] private float cableLength = 0.5f;
    [SerializeField] private int totalSegments = 5;
    [SerializeField] private float segmentsPerUnit = 2f;
    private int segments = 0;
    [SerializeField] private float cableWidth = 0.1f;

    // Solver config
    [SerializeField] private int verletIterations = 1;
    [SerializeField] private int solverIterations = 1;

    private LineRenderer line;
    private CableParticle[] points;

    #endregion

    #region Initial setup

    public void ManualSetup(Transform endPoint)
    {
        this.endPoint = endPoint;
        Setup();
    }

    private void Start()
    {
        if (manualStart)
            return;

        Setup();
    }

    private void Setup()
    {
        InitCableParticles();
        InitLineRenderer();
    }

    /**
     * Init cable particles
     *
     * Creates the cable particles along the cable length
     * and binds the start and end tips to their respective game objects.
     */
    private void InitCableParticles()
    {
        // Calculate segments to use
        if (totalSegments > 0)
            segments = totalSegments;
        else
            segments = Mathf.CeilToInt(cableLength * segmentsPerUnit);

        var cableDirection = (endPoint.position - transform.position).normalized;
        var initialSegmentLength = cableLength / segments;
        points = new CableParticle[segments + 1];

        // Foreach point
        for (var pointIdx = 0; pointIdx <= segments; pointIdx++)
        {
            // Initial position
            var initialPosition = transform.position + (cableDirection * (initialSegmentLength * pointIdx));
            points[pointIdx] = new CableParticle(initialPosition);
        }

        // Bind start and end particles with their respective gameobjects
        var start = points[0];
        var end = points[segments];
        start.Bind(this.transform);
        end.Bind(endPoint.transform);
    }

    /**
     * Initialized the line renderer
     */
    private void InitLineRenderer()
    {
        line = gameObject.AddComponent<LineRenderer>();
        line.startWidth = cableWidth;
        line.endWidth = cableWidth;
        line.positionCount = segments + 1;
        line.material = cableMaterial;
        line.numCapVertices = 4;
        line.numCornerVertices = 2;
        line.GetComponent<Renderer>().enabled = true;
    }

    #endregion

    #region Render Pass

    private void LateUpdate()
    {
        // player joining during already starting game fails to setup
        if (points == null)
        {
            enabled = false;
            return;
        }
        

        for (var verletIdx = 0; verletIdx < verletIterations; verletIdx++)
        {
            VerletIntegrate();
            SolveConstraints();
        }
        
        points[^1].Position = endPoint.position;
        RenderCable();
    }

    /**
     * Render Cable
     *
     * Update every particle position in the line renderer.
     */
    private void RenderCable()
    {
        for (var pointIdx = 0; pointIdx < segments + 1; pointIdx++)
        {
            line.SetPosition(pointIdx, points[pointIdx].Position);
        }
    }

    #endregion

    #region Verlet integration & solver pass

    private void FixedUpdate()
    {
        // player joining during already starting game fails to setup
        if (points == null)
        {
            enabled = false;
            return;
        }

    }

    /**
     * Verlet integration pass
     *
     * In this step every particle updates its position and speed.
     */
    private void VerletIntegrate()
    {
        var gravityDisplacement = Time.fixedDeltaTime * Time.fixedDeltaTime * Physics.gravity;
        foreach (var particle in points)
        {
            particle.UpdateVerlet(gravityDisplacement);
        }
    }

    /**
     * Constrains solver pass
     *
     * In this step every constraint is addressed in sequence
     */
    private void SolveConstraints()
    {
        // For each solver iteration..
        for (var iterationIdx = 0; iterationIdx < solverIterations; iterationIdx++)
        {
            SolveDistanceConstraint();
            SolveStiffnessConstraint();
        }
    }

    #endregion

    #region Solver Constraints

    /**
     * Distance constraint for each segment / pair of particles
     **/
    private void SolveDistanceConstraint()
    {
        var segmentLength = cableLength / segments;
        for (var SegIdx = 0; SegIdx < segments; SegIdx++)
        {
            var particleA = points[SegIdx];
            var particleB = points[SegIdx + 1];

            // Solve for this pair of particles
            SolveDistanceConstraint(particleA, particleB, segmentLength);
        }
    }

    /**
     * Distance Constraint
     *
     * This is the main constrains that keeps the cable particles "tied" together.
     */
    private void SolveDistanceConstraint(CableParticle particleA, CableParticle particleB, float segmentLength)
    {
        // Find current vector between particles
        var delta = particleB.Position - particleA.Position;
        // 
        var currentDistance = delta.magnitude;
        var errorFactor = (currentDistance - segmentLength) / currentDistance;

        // Only move free particles to satisfy constraints
        if (particleA.IsFree() && particleB.IsFree())
        {
            particleA.Position += errorFactor * 0.5f * delta;
            particleB.Position -= errorFactor * 0.5f * delta;
        }
        else if (particleA.IsFree())
        {
            particleA.Position += errorFactor * delta;
        }
        else if (particleB.IsFree())
        {
            particleB.Position -= errorFactor * delta;
        }
    }

    /**
     * Stiffness constraint
     **/
    private void SolveStiffnessConstraint()
    {
        var distance = (points[0].Position - points[segments].Position).magnitude;
        if (distance > cableLength)
        {
            foreach (var particle in points)
            {
                SolveStiffnessConstraint(particle, distance);
            }
        }
    }

    /**
     * TODO: I'll implement this constraint to reinforce cable stiffness
     *
     * As the system has more particles, the verlet integration aproach
     * may get way too loose cable simulation. This constraint is intended
     * to reinforce the cable stiffness.
     * // throw new System.NotImplementedException ();
     **/
    private void SolveStiffnessConstraint(CableParticle cableParticle, float distance)
    {
    }

    #endregion
}