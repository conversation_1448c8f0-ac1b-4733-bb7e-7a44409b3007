using Consts;
using Rewired;
using UnityEngine;
using Utils.Extensions;

[HelpURL(StringConsts.DocumentationLink + "NT-A-8")]
public abstract class GenericTool : GenericGrabbable, ITool
{
    protected const string FPSSettingsFoldoutGroup = "FPS Settings";

    public abstract int IdleAnimationTrigger { get; }
    public virtual bool AllowGrabbingItems => false;

    [SerializeField] protected Vector3 syncOffsetPosition = Vector3.zero;
    [SerializeField] protected Quaternion syncOffsetRotation = Quaternion.identity;
    [SerializeField] private GameObject packaging; 

    protected Rewired.Player rewiredPlayer;
    protected bool useStarted;

    public static readonly ItemsContainer<GenericTool> ToolItemsData = new();
    
    protected virtual void Awake()
    {
        rewiredPlayer = ReInput.players.GetPlayer(0);
    }

    public override void OnNetworkSpawn()
    {
        base.OnNetworkSpawn();

        if (IsServer)
        {
            // only works with spawned objects
            NetworkObject.SceneMigrationSynchronization = true;
            NetworkObject.DestroyWithScene = false;

            gameObject.MoveToWorld();
        }

        ToolItemsData.AddItem(this);
        SetPhysicsLayer(LayerConsts.Interactable);
    }

    public override void OnNetworkDespawn()
    {
        base.OnNetworkDespawn();
        
        ToolItemsData.RemoveItem(this);
    }

    #region IIntractable

    public override bool RequestInteraction(InteractAction interactor)
    {
        return interactor.EquippedTool == null && base.RequestInteraction(interactor);
    }

    public override bool RequestInteractionWithTool(InteractAction interactor, ITool toolInHands)
    {
        return false;
    }

    #endregion

    #region IGrabbable

    public override void SyncPositionRotation(Transform holdingTransform)
    {
        transform.SetPositionAndRotation(holdingTransform.position + holdingTransform.rotation * syncOffsetPosition, holdingTransform.rotation * syncOffsetRotation);
    }

    protected override void SetParametersOriginalClientSide(bool isGrabbed)
    {
        SetPhysicsLayer(isGrabbed ? LayerConsts.FPS : LayerConsts.Interactable);
    }

    public override void DropOriginalClientSide(ulong clientID, InteractAction interactor)
    {
        base.DropOriginalClientSide(clientID, interactor);
        OnUseEnded();
    }

    #endregion

    #region ITool

    /// <summary>
    /// Check <see cref="useStarted"/> before logic, if applicable
    /// </summary>
    public abstract void OnUseActive();

    public virtual void OnUseStarted()
    {
        useStarted = true;
    }

    public virtual void OnUseEnded()
    {
        useStarted = false;
    }

    /// <summary>
    /// Only means a specific type of active 'using' interaction, like a garbage bag, which wants to put interacted item inside of it. Or gravi-gloves, which want to grab that object.
    /// Is not triggered on other tools.
    /// </summary>
    public virtual void OnInteractUsingThisTool(InteractAction interactor, IInteractable interactable, out bool performNormalInteraction)
    {
        performNormalInteraction = false;
        interactor.AnimatorsSetTrigger(AnimatorConsts.ActionTrigger);
    }

    public virtual void OnDropUsingThisTool(InteractAction interactor, bool withForce) { }

    public virtual void TriggerAuxAnimation() { }

    #endregion

    public override void InteractClientSide(IGrabbable itemInHands, bool isOwner, Vector3 raycastPoint, Vector3 headDirection, ulong clientId, InteractAction interactor)
    {
        base.InteractClientSide(itemInHands, isOwner, raycastPoint, headDirection, clientId, interactor);

        if (isGrabbed && packaging != null)
        {
            packaging.SetActive(false); 
        }
    }

    public override Color GetOutlineColor()
    {
        return new Color(0.345f, 0.502f, 0.302f, 1f); 
    }

}